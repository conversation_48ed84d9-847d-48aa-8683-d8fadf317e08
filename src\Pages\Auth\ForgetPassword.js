import React, { useEffect, useState } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import "./Auth.css";
import { useNavigate } from "react-router-dom";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { forgotEmail } from "./hooks/forgotEmail";
import { toast } from "react-toastify";
import { ToastContainer, Zoom } from "react-toastify";
import MainLogo from "../../Dexta_assets/LoginLogov4.png";
import Email from "../../Dexta_assets/email-image.png";
import TextField from "../../Components/Dexta/TextField/TextField";
import CustomButton from "../../Components/CustomButton/CustomButton";
import { FaArrowRightLong } from "react-icons/fa6";
import { useTranslation } from "react-i18next";

const ForgetPassword = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [toastError, setToasterror] = useState("");
  const [toastSuccess, setToastSuccess] = useState(false);

  //#region api call for forget passwowrd
  const { mutate: mutateEmail, isLoading: emailLoading } = useMutation(
    forgotEmail,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/auth/forgot-password");
        if (response.message === "Reset password email sent")
          setToastSuccess(t("reset_password_email_sent"));
        navigate(
          `/forget-password-email-sent/?email=${validation?.values?.email}`
        );
      },
      onError: (error) => {
        if (error.response.data.message[0] != "")
          setToasterror(t("invalid_email"));
      },
    }
  );
  //#endregion

  //#region success and error toasts
  useEffect(() => {
    if (toastSuccess != "") {
      toast.success(toastSuccess, {
        toastId: "copy-success",
      });
    }
    setTimeout(() => {
      setToastSuccess("");
    }, 1000);
  }, [toastSuccess]);

  useEffect(() => {
    if (toastError != "") {
      toast.success(toastError, {
        toastId: "copy-success",
      });
    }
    setTimeout(() => {
      setToasterror("");
    }, 1000);
  }, [toastError]);
  //#endregion

  //#region formik validations and handle submit function
  const validation = useFormik({
    enableReinitialize: true,
    initialValues: {
      email: "",
    },
    validationSchema: Yup.object({
      email: Yup.string().required(t("required_field")),
    }),
    onSubmit: (values) => {
      let data = JSON.stringify({
        email: values?.email,
      });
      try {
        mutateEmail(data);
      } catch (error) {
        //onError will automatically detect
      }
    },
  });
  //#endregion

  document.title = "Forget Password | Dexta";

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <img src={MainLogo} alt="Logo" className="mx-auto h-12" />
          <h2 className="mt-6 text-3xl font-bold text-gray-900">{t("forgot_password")}</h2>
          <p className="mt-2 text-sm text-gray-600">{t("check_email")}</p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={validation.handleSubmit}>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              {t("email")}
            </label>
            <TextField
              type="email"
              name="email"
              placeholder={t("enter_email")}
              border="border border-[#D3D5D8] focus-within:border focus-within:border-coalColor"
              onChange={validation.handleChange}
              onBlur={validation.handleBlur}
              value={validation.values.email}
              rounded="rounded-md"
            />
            {validation.touched.email && validation.errors.email && (
              <p className="mt-1 text-sm text-red-600">{t("invalid_email")}</p>
            )}
          </div>

          <div>
            <CustomButton
              type="submit"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              isLoading={emailLoading}
            >
              {t("reset_password")}
            </CustomButton>
          </div>

          <div className="text-center">
            <button
              type="button"
              onClick={() => navigate("/login")}
              className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
            >
              {t("return_to_login")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ForgetPassword;
