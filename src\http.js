import axios from "axios";

const instance = axios.create({
  baseURL: process.env.REACT_APP_Server,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
    "x-role": "recruiter",
  },
});

instance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("CP-USER-TOKEN");
    if (token) {
      config.headers["Authorization"] = "Bearer " + token; // for Spring Boot back-end
      // config.headers["x-token"] = token; // for Node.js Express back-end
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
var count = 0;

instance.interceptors.response.use(
  (res) => {
    return res;
  },
  async (err) => {
    const originalConfig = err.config;

    if (originalConfig.url !== "/auth/sign-in" && err?.response) {
      // Access Token was expired
      if (err?.response?.status === 401 && !originalConfig?._retry) {
        const dscObj = localStorage.getItem("DSC_OBJ");

        localStorage.clear();

        if (dscObj) {
          localStorage.setItem("DSC_OBJ", dscObj);
        }

        if (
          !window.location.pathname.includes("/login") &&
          !window.location.pathname.includes("/register") &&
          !window.location.pathname.includes("/place_order")
        ) {
          window.location = "/login";
        }

        originalConfig._retry = true;
        if (count < 2) {
          count = count + 1;
          try {
            const rs = await instance.post("/auth/refresh-access");

            if (rs.data) {
              localStorage.setItem("CP-USER-TOKEN", rs?.data?.access?.token);
            }
            return instance(originalConfig);
          } catch (_error) {
            localStorage.clear();

            if (dscObj) {
              localStorage.setItem("DSC_OBJ", dscObj);
            }
            if (
              !window.location.pathname.includes("/login") &&
              !window.location.pathname.includes("/register") &&
              !window.location.pathname.includes("/place_order")
            ) {
              window.location = "/login";
            }

            return Promise.reject(_error);
          }
        } else {
        }
      }
    }

    return Promise.reject(err);
  }
);

export default instance;
