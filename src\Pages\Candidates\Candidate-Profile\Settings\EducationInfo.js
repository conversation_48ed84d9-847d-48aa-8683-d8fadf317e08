import React, { useState, useEffect, useRef } from "react";
import TextField from "../../../../Components/Dexta/TextField/TextField";
import { IoIosArrowDown, IoMdClose } from "react-icons/io";
import Loader from "react-loader-spinner";
import { toast } from "react-toastify";
import { updateUser } from "../../hooks/updateUser";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateCandidate } from "../../hooks/updateCandidate";
import { highestEducation } from "../../../../data/mapData";
import useOutsideClick from "../../../../Components/OutsideClick/OutsideClick";
import { useTranslation } from "react-i18next";
import { LanguageSwitcher } from "../../../../Helpers/LanguageSwitcher";
import i18n from "../../../../i18n";
import { labelToCode, languageMap } from "../../../../Helpers/languageHelper";

const EducationInfo = ({
  educationLevel,
  setEducationLevel,
  study,
  setStudy,
}) => {
  const queryClient = useQueryClient();
  const educationref = useRef(null);
  const [educationDrop, setEducationDrop] = useState(false);
  const [otherEducation, setOtherEducation] = useState("");
  const { t } = useTranslation();
  const languageRef = useRef(null);
  const [languageDrop, setLanguageDrop] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(() => {
    const storedLanguage = localStorage.getItem("i18nextLng") || "en";
    return languageMap[storedLanguage] || "English";
  });
  console.log(educationLevel, "hello jee");

  const educationTranslationMap = {
    "Primary Education": "Primary_Education",
    "Secondary Education (High School)": "Secondary_Education_High_School",
    "Vocational/Technical Training": "Vocational_Technical_Training",
    "College / University (No Degree)": "College___University_No_Degree",
    "Associate Degree": "Associate_Degree",
    "Bachelor's Degree": "Bachelor_s_Degree",
    "Master's Degree": "Master_s_Degree",
    "Professional Degree (e.g., MD, JD)": "Professional_Degree_e_g__MD_JD",
    "Doctorate (PhD)": "Doctorate_PhD",
    "Other (Please specify)": "Other_Please_specify",
  };

  //#region use Formik and yup to handle forms
  const HandleEducationSubmit = () => {
    let data = JSON.stringify({
      candidateMeta: {
        educationLevel:
          educationLevel === "Highest education level" ? "" : educationLevel,
        study: study,
      },
    });
    try {
      updateMutate(data);
    } catch (error) {
      //onError will automatically detect
    }
  };
  //#endregion

  //#region mutate to update data
  const { mutate: updateMutate, isLoading: mutateLoadings } = useMutation(
    updateUser,
    {
      onSuccess: () => {
        queryClient.invalidateQueries("users");
        handleOnBoard();
        toast.success("Information Saved", {
          toastId: "copy-success",
        });
      },
      onError: (error) => {
        setTimeout(() => {
          toast.error(error?.response?.data?.message[0], {
            toastId: "copy-success",
          });
        }, 500);
      },
    }
  );
  //#endregion

  //#region Updating Tour Completed
  const handleOnBoard = () => {
    let data = JSON.stringify({
      isUserOnboard: true,
    });
    try {
      updateCandidateUserMutate(data);
    } catch (err) {
      console.log(err.message);
    }
  };
  //#endregion

  //#region updating tour status
  const { mutate: updateCandidateUserMutate, isLoading: updateLoading } =
    useMutation(updateCandidate, {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/users");
      },
    });
  //#endregion

  //#region Dropdown close on outside click -> Country
  useOutsideClick(
    [
      {
        ref: educationref,
        excludeClasses: [".educationClass"],
        excludeIds: ["education"],
      },
    ],
    (ref) => {
      if (ref === educationref) setEducationDrop(false);
    }
  );
  //#endregion

  //#region placeholder text
  useEffect(() => {
    if (educationLevel === "") {
      setEducationLevel("Highest education level");
    }
  }, [educationLevel]);
  //#endregion

  useEffect(() => {
    const langCode = labelToCode[selectedLanguage];
    if (langCode && langCode !== i18n.language) {
      i18n.changeLanguage(langCode);
    }
  }, [selectedLanguage]);

  useEffect(() => {
    function handleOutsideClick(event) {
      if (
        languageRef.current &&
        !languageRef.current.contains(event.target) &&
        event.target.id !== "language" &&
        !event.target.closest(".languageClass")
      ) {
        setLanguageDrop(false);
      }
    }
    document.addEventListener("mousedown", handleOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [languageRef]);

  return (
    <>
      <div className="p-3">
        <h1
          style={{
            fontFamily: "Archia Semibold",
          }}
        >
          {t("Candidate-Settings.education_information")}
        </h1>
        <div className="grid md:grid-cols-2 gap-8 mt-5">
          <div>
            <p className="mb-2" style={{ fontFamily: "Silka" }}>
              {t("Candidate-Settings.highest_education_level")}
            </p>
            <div className="h-10">
              <div className="relative">
                <button
                  id="education"
                  className={`border border-[#D3D5D8] ${
                    educationLevel === "Highest education level" ||
                    educationLevel === null
                      ? "text-[#999999]"
                      : "text-coalColor"
                  }  focus-within:border focus-within:border-coalColor rounded-md px-3 text-left w-full py-4`}
                  value={educationLevel}
                  style={{ fontFamily: "Silka" }}
                  onClick={() => setEducationDrop(!educationDrop)}
                  type="button"
                >
                  {educationLevel != "" && educationLevel != null ? (
                    educationLevel === "Other (Please specify)" ? (
                      <input
                        placeholder={t("Education-Info.please_specify")}
                        autoFocus
                        value={otherEducation}
                        onChange={(e) => setOtherEducation(e.target.value)}
                      />
                    ) : educationLevel === "Highest education level" ? (
                      t("Education-Info.highest_education_level")
                    ) : (
                      t(
                        `Education-Info.dropdown.${
                          educationTranslationMap[educationLevel] ||
                          educationLevel
                        }`
                      )
                    )
                  ) : (
                    t("Education-Info.highest_education_level")
                  )}
                  <div className="absolute top-1/2 right-3 transform -translate-y-1/2 flex flex-row ">
                    <IoMdClose
                      onClick={() => {
                        setEducationLevel("");
                      }}
                      className="cursor-pointer"
                    />
                    <IoIosArrowDown />
                  </div>
                </button>
                {educationDrop && (
                  <div className="educationClass absolute z-40 border border-coalColor right-0 top-full h-[80 overflow-scroll bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] w-full mt-2">
                    {highestEducation?.map((i) => (
                      <ul
                        key={i.title}
                        className="text-sm text-coalColor rounded hover:bg-coalColor hover:text-white cursor-pointer"
                        ref={educationref}
                        onClick={() => {
                          setEducationLevel(i?.title);
                          setEducationDrop(false);
                        }}
                      >
                        <li>
                          {" "}
                          <a className="block px-5 py-2 text-sm font-medium">
                            {" "}
                            {t(
                              `Education-Info.dropdown.${
                                educationTranslationMap[i.title] || i.title
                              }`
                            )}
                          </a>
                        </li>
                      </ul>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div>
            <p className="mb-2" style={{ fontFamily: "Silka" }}>
              {t("Candidate-Settings.what_did_you_study")}
            </p>
            <div className="h-10">
              <TextField
                type="text"
                name="study"
                label={t("Candidate-Settings.what_did_you_study")}
                rounded="rounded-md"
                border={`border mt-3 border-[#D3D5D8] focus-within:border focus-within:border-coalColor `}
                value={study}
                onChange={(e) => setStudy(e.target.value)}
                placeholder={t("Candidate-Settings.what_did_you_study")}
              />
            </div>
          </div>
        </div>
        <hr className="w-full mt-14 bg-[#D3D5D8] border-1" />
        <h1
          className="mt-5"
          style={{
            fontFamily: "Archia Semibold",
          }}
        >
          {t("Candidate-Settings.language")}
        </h1>
        {/* <p className="mt-3" style={{ fontFamily: "Silka" }}>
          Choose the language in which you'd like to use Dexta. You will also
          receive communications in this language.
        </p> */}
        <div className="grid md:grid-cols-2 gap-8 mt-5">
          <div className="relative w-full">
            <button
              id="language"
              className={`rounded-md border border-[#D3D5D8] text-sm py-4 px-3 w-full bg-white text-left flex items-center text-coalColor`}
              type="button"
              onClick={() => setLanguageDrop(!languageDrop)}
              style={{ fontFamily: "Silka" }}
            >
              {selectedLanguage}
              <svg
                className="w-2.5 h-2.5 ml-auto"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 10 6"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 1 4 4 4-4"
                />
              </svg>
            </button>
            {languageDrop && (
              <div
                id="language-drop"
                className="absolute languageClass z-40 right-0 top-full h-auto mt-2 bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] w-full "
                ref={languageRef}
              >
                {["English", "Spanish"].map((lang, index) => (
                  <ul
                    key={index}
                    className="text-sm text-coalColor rounded hover:bg-coalColor hover:text-white cursor-pointer"
                    style={{ fontFamily: "Silka" }}
                    onClick={() => {
                      setSelectedLanguage(lang);
                      setLanguageDrop(false);
                    }}
                  >
                    <li>
                      <a className="block px-5 py-2 text-sm font-medium">
                        {lang}
                      </a>
                    </li>
                  </ul>
                ))}
              </div>
            )}
          </div>
          <div></div>
        </div>
        <hr className="w-full mt-[4rem] bg-[#D3D5D8] border-1" />
      </div>
      <div className="mb-3 mt-6 items-center flex justify-end px-1">
        <button
          type="submit"
          name="Sign up"
          onClick={HandleEducationSubmit}
          className="bg-primaryGreen hover:bg-coalColor hover:text-white sm:w-full md:w-1/6 h-12 rounded-md text-coalColor border border-coalColor font-medium text-md relative"
          style={{ fontFamily: "Archia Semibold" }}
        >
          {mutateLoadings || updateLoading ? (
            <span className="flex items-center justify-center">
              <Loader type="Oval" color="black" height={20} width={20} />
              <span className="ml-2">{t("Candidate-Settings.saving")}</span>
            </span>
          ) : (
            t("Candidate-Settings.save")
          )}
        </button>
      </div>
    </>
  );
};

export default EducationInfo;
